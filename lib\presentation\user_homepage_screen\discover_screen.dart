import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class DiscoverScreen extends StatefulWidget {
  @override
  _DiscoverScreenState createState() => _DiscoverScreenState();
}

class _DiscoverScreenState extends State<DiscoverScreen> {
  int selectedButton = 0;

  // Function to handle button selection
  void onButtonPressed(int index) {
    setState(() {
      selectedButton = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: Text(
          'Discover',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Row of Buttons
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildCategoryButton(0, 'News', 'assets/fire.svg'),
                    SizedBox(width: 10),
                    _buildCategoryButton(1, 'Events', 'assets/airplay.svg'),
                    SizedBox(width: 10),
                    _buildCategoryButton(2, 'Markets Place', 'assets/bar.svg'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Card below the buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Card(
                elevation: 2,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        // Image on the left
                        Container(
                          width: 100, // Fixed width for the image
                          height: 100,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/event.jpg'),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(15),
                              bottomLeft: Radius.circular(15),
                              bottomRight: Radius.circular(15),
                              topRight: Radius.circular(15),
                            ),
                          ),
                        ),

                        // Description on the right
                        Expanded(
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              'Description about the card goes here. You can add details about the festival, event, or offer.',
                              style: TextStyle(
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Row below the description
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8.0, vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Sports',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F41BB),
                              fontFamily: "Satoshi",
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                '20',
                                style: TextStyle(
                                  color: Colors.black54,
                                  fontFamily: "Satoshi",
                                ),
                              ),
                              SizedBox(width: 5),
                              Icon(
                                Icons.favorite, // Filled favorite icon
                                color: Color(0xFF1F41BB),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(
              width: 10,
            ),
            // Card below the buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Card(
                elevation: 2,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        // Image on the left
                        Container(
                          width: 100, // Fixed width for the image
                          height: 100,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/event.jpg'),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(15),
                              bottomLeft: Radius.circular(15),
                              bottomRight: Radius.circular(15),
                              topRight: Radius.circular(15),
                            ),
                          ),
                        ),

                        // Description on the right
                        Expanded(
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              'Description about the card goes here. You can add details about the festival, event, or offer.',
                              style: TextStyle(
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Row below the description
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8.0, vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Sports',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F41BB),
                              fontFamily: "Satoshi",
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                '20',
                                style: TextStyle(
                                  color: Colors.black54,
                                  fontFamily: "Satoshi",
                                ),
                              ),
                              SizedBox(width: 5),
                              Icon(
                                Icons.favorite, // Filled favorite icon
                                color: Color(0xFF1F41BB),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(
              width: 10,
            ),
            // Card below the buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Card(
                elevation: 2,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        // Image on the left
                        Container(
                          width: 100, // Fixed width for the image
                          height: 100,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/event.jpg'),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(15),
                              bottomLeft: Radius.circular(15),
                              bottomRight: Radius.circular(15),
                              topRight: Radius.circular(15),
                            ),
                          ),
                        ),

                        // Description on the right
                        Expanded(
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              'Description about the card goes here. You can add details about the festival, event, or offer.',
                              style: TextStyle(
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Row below the description
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8.0, vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Sports',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F41BB),
                              fontFamily: "Satoshi",
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                '20',
                                style: TextStyle(
                                  color: Colors.black54,
                                  fontFamily: "Satoshi",
                                ),
                              ),
                              SizedBox(width: 5),
                              Icon(
                                Icons.favorite, // Filled favorite icon
                                color: Color(0xFF1F41BB),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(
              width: 10,
            ),
            // Card below the buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Card(
                elevation: 2,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        // Image on the left
                        Container(
                          width: 100, // Fixed width for the image
                          height: 100,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/event.jpg'),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(15),
                              bottomLeft: Radius.circular(15),
                              bottomRight: Radius.circular(15),
                              topRight: Radius.circular(15),
                            ),
                          ),
                        ),

                        // Description on the right
                        Expanded(
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              'Description about the card goes here. You can add details about the festival, event, or offer.',
                              style: TextStyle(
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Row below the description
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8.0, vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Sports',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F41BB),
                              fontFamily: "Satoshi",
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                '20',
                                style: TextStyle(
                                  color: Colors.black54,
                                  fontFamily: "Satoshi",
                                ),
                              ),
                              SizedBox(width: 5),
                              Icon(
                                Icons.favorite, // Filled favorite icon
                                color: Color(0xFF1F41BB),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryButton(int index, String title, String iconPath) {
    return ElevatedButton(
      onPressed: () => onButtonPressed(index),
      style: ElevatedButton.styleFrom(
        foregroundColor:
            selectedButton == index ? Colors.white : Color(0xFF1F41BB),
        backgroundColor:
            selectedButton == index ? Color(0xFF1F41BB) : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        side: BorderSide(color: Color(0xFF1F41BB)),
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(iconPath, width: 20, height: 20),
          SizedBox(width: 8),
          Text(title),
        ],
      ),
    );
  }
}
