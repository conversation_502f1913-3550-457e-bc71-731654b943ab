import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tareek/presentation/user_homepage_screen/discover_screen.dart';
import 'package:tareek/widgets/bottom_navigation.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: BottomNavBar(),
    );
  }
}

class HomeScreen extends StatefulWidget {
  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int selectedButton = 0;

  // Function to handle button selection
  void onButtonPressed(int index) {
    setState(() {
      selectedButton = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Color(0xFF1F41BB),
        elevation: 0,
        title: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Good Evening!',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      fontFamily: "Satoshi",
                    ),
                  ),
                  SizedBox(height: 5),
                  Text(
                    'John Doe',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      fontFamily: "Satoshi",
                    ),
                  ),
                ],
              ),
              CircleAvatar(
                radius: 25,
                backgroundImage: AssetImage('assets/account.jpg'),
              ),
            ],
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              color: Color(0xFF1F41BB),
              padding: EdgeInsets.all(20),
              child: Column(
                children: [
                  SizedBox(height: 20),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                        bottomLeft: Radius.circular(30),
                        bottomRight: Radius.circular(30),
                      ),
                    ),
                    child: TextField(
                      decoration: InputDecoration(
                        prefixIcon: Icon(Icons.search),
                        suffixIcon: Icon(Icons.filter_list),
                        hintText: 'Search...',
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(vertical: 15),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Popular Now Section
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Text(
                    'Popular Now',
                    style: TextStyle(
                      fontSize: 18,
                      fontFamily: "Satoshi",
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(width: 10),
                  SvgPicture.asset('assets/fire.svg', width: 20, height: 20),
                ],
              ),
            ),

            // Scrollable Row of Cards
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // Card 1
                  Container(
                    width: 200,
                    margin: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    child: Card(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Image with a fixed height
                          Container(
                            height: 150,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: AssetImage('assets/event.jpg'),
                                fit: BoxFit.cover,
                              ),
                              borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(15)),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                SvgPicture.asset('assets/time_fill.svg',
                                    width: 20, height: 20),
                                Text('2 Hours Today'),
                                const SizedBox(width: 10),
                                Text(
                                  'Festival',
                                  style: TextStyle(
                                    color: Color(0xFF1F41BB),
                                    fontWeight: FontWeight.bold,
                                    fontFamily: "Satoshi",
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              'Description about the card goes here. You can add details about the festival, event, or offer.',
                              style: TextStyle(
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Card 2
                  Container(
                    width: 200,
                    margin: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    child: Card(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: 150,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: AssetImage('assets/event1.jpg'),
                                fit: BoxFit.cover,
                              ),
                              borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(15)),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                SvgPicture.asset('assets/time_fill.svg',
                                    width: 20, height: 20),
                                Text(
                                  '2 Hours Today',
                                  style: TextStyle(
                                    fontFamily: "Satoshi",
                                  ),
                                ),
                                Text(
                                  'Sports',
                                  style: TextStyle(
                                    color: Color(0xFF1F41BB),
                                    fontWeight: FontWeight.bold,
                                    fontFamily: "Satoshi",
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 16.0),
                            child: Text(
                              'Description about the card goes here. You can add details about the festival, event, or offer.',
                              style: TextStyle(
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Category Section
            Padding(
              padding: EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Category',
                    style: TextStyle(
                      fontSize: 18,
                      fontFamily: "Satoshi",
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => DiscoverScreen(),
                        ),
                      );
                    },
                    child: Text(
                      'See All',
                      style: TextStyle(
                        fontFamily: "Satoshi",
                        color: Color(0xFF1F41BB),
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Row of Buttons
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildCategoryButton(0, 'News', 'assets/fire.svg'),
                    SizedBox(
                      width: 10,
                    ),
                    _buildCategoryButton(1, 'Events', 'assets/airplay.svg'),
                    SizedBox(
                      width: 10,
                    ),
                    _buildCategoryButton(2, 'Markets Place', 'assets/bar.svg'),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            // Card below the buttons
            Card(
              elevation: 2,
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // Image on the left
                      Container(
                        width: 100, // Fixed width for the image
                        height: 100,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage('assets/event.jpg'),
                            fit: BoxFit.cover,
                          ),
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(15),
                            bottomLeft: Radius.circular(15),
                            bottomRight: Radius.circular(15),
                            topRight: Radius.circular(15),
                          ),
                        ),
                      ),

                      // Description on the right
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Text(
                            'Description about the card goes here. You can add details about the festival, event, or offer.',
                            style: TextStyle(
                              color: Colors.black54,
                              fontFamily: "Satoshi",
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Row below the description
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Sports',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF1F41BB),
                            fontFamily: "Satoshi",
                          ),
                        ),
                        Row(
                          children: [
                            Text(
                              '20',
                              style: TextStyle(
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                            ),
                            SizedBox(width: 5),
                            Icon(
                              Icons.favorite, // Filled favorite icon
                              color: Color(0xFF1F41BB),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryButton(int index, String title, String iconPath) {
    return ElevatedButton(
      onPressed: () => onButtonPressed(index),
      style: ElevatedButton.styleFrom(
        foregroundColor:
            selectedButton == index ? Colors.white : Color(0xFF1F41BB),
        backgroundColor:
            selectedButton == index ? Color(0xFF1F41BB) : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        side: BorderSide(color: Color(0xFF1F41BB)),
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(iconPath, width: 20, height: 20),
          SizedBox(width: 8),
          Text(title),
        ],
      ),
    );
  }
}
