/// API endpoints configuration for the Tareek application
class UserApiEndpoints {
  // Base URL
  static const String baseUrl = 'https://taraq-go.onrender.com';
  // API version
  // static const String apiVersion = '/api/v1';
  // Full base URL with version
  static const String fullBaseUrl = '$baseUrl';

  // User Authentication endpoints
  static const String register = '/auth/api/register';
  static const String login = '/auth/api/login';
  static const String verifyUser = '/auth/api/verify';
  static const String resendOtp = '/auth/api/resendOtp';
  static const String forgotPassword = '/auth/api/forgotPassword';
  static const String resetPassword = '/auth/api/resetPassword';

  // User endpoints
  static const String userUpdateProfile = '/user/api/update'; // + /{id}
  static const String userOnboarding = '/user/api/onboarding'; // + /{id}
  static const String userChangePassword =
      '/user/api/reset-password'; // + /{id}
  static const String userUpdateDetails = '/user/api/update-details'; // + /{id}
  static const String userLogout = '/user/api/logout';

  // User Notification endpoints
  static const String userNotifications = '/user/api/notifications'; // + /{id}
  static const String markUserNotificationAsRead =
      '/user/api/notifications/mark-read'; // + /{id}

  // User Favorite News endpoints
  static const String userFavoriteNews = '/user/api/news/favorite';
  static const String addNewsToUserFavorites = '/user/api/news/favorite';
  static const String removeNewsFromUserFavorites =
      '/user/api/news/favorite'; // + /{id}

  // User Favorite Events endpoints
  static const String userFavoriteEvents = '/user/api/events/favorite';
  static const String addEventToUserFavorites = '/user/api/events/favorite';
  static const String removeEventFromUserFavorites =
      '/user/api/events/favorite'; // + /{id}
  static const String checkIfUserLikedEvent = '/user/api/events/check-like';
  static const String userToLikeEvent = '/user/api/events/like';
  static const String userToUnlikeEvent = '/user/api/events/unlike';
  static const String userToSendEventOpinion = '/user/api/events/opinion';
  static const String userToShareEvent = '/user/api/events/share';

  // User Favorite Job Posts endpoints
  static const String userFavoriteJobPosts = '/user/api/job-post/favorite';
  static const String addJobPostToUserFavorites = '/user/api/job-post/favorite';
  static const String removeJobPostFromUserFavorites =
      '/user/api/job-post/favorite'; // + /{id}

  // User Job Application
  static const String userApplyForJob = '/user/api/job-applications';
  static const String userJobRecommendations =
      '/user/api/job-recommendation'; // + /{user_id}
  static const String addUserFavoriteJobPost = "/user/api/job-post/favorite";
  static const String RemoveUserFavoriteJobPost = "/user/api/job-post/favorite";

  // Blog endpoints
  static const String allBlogs = '/common/api/blog';
  static const String blogById = '/common/api/blog'; // + /{id}

  // Events endpoints
  static const String allEvents = '/common/api/events';
  static const String eventById = '/common/api/events'; // + /{id}

  // Jobs endpoints
  static const String allJobPosts = '/common/api/job-post';
  static const String jobPostById = '/common/api/job-post'; // + /{id}

  // Helper methods to build full URLs
  static String getFullUrl(String endpoint) {
    return '$fullBaseUrl$endpoint';
  }

  // Authentication URL helpers
  static String getRegisterUrl() {
    return '$fullBaseUrl$register';
  }

  static String getLoginUrl() {
    return '$fullBaseUrl$login';
  }

  static String getVerifyUserUrl() {
    return '$fullBaseUrl$verifyUser';
  }

  static String getResendOtpUrl() {
    return '$fullBaseUrl$resendOtp';
  }



  static String getForgotPasswordUrl() {
    return '$fullBaseUrl$forgotPassword';
  }

  static String getResetPasswordUrl() {
    return '$fullBaseUrl$resetPassword';
  }

  // User profile URL helpers
  static String getUserUpdateProfileUrl(String userId) {
    return '$fullBaseUrl$userUpdateProfile/$userId';
  }

  static String getUserOnboardingUrl(String userId) {
    return '$fullBaseUrl$userOnboarding/$userId';
  }

  static String getUserChangePasswordUrl(String userId) {
    return '$fullBaseUrl$userChangePassword/$userId';
  }

  static String getUserUpdateDetailsUrl(String userId) {
    return '$fullBaseUrl$userUpdateDetails/$userId';
  }

  static String getUserLogoutUrl() {
    return '$fullBaseUrl$userLogout';
  }

  // User notification URL helpers
  static String getUserNotificationsUrl(String userId) {
    return '$fullBaseUrl$userNotifications/$userId';
  }

  static String getNotificationReadUrl(String notificationId) {
    return '$fullBaseUrl$markUserNotificationAsRead/$notificationId/read';
  }

  // User favorite news URL helpers
  static String getUserFavoriteNewsUrl() {
    return '$fullBaseUrl$userFavoriteNews';
  }

  static String getAddNewsToUserFavoritesUrl() {
    return '$fullBaseUrl$addNewsToUserFavorites';
  }

  static String getRemoveNewsFromUserFavoritesUrl(String newsId) {
    return '$fullBaseUrl$removeNewsFromUserFavorites/$newsId';
  }

  // User favorite events URL helpers
  static String getUserFavoriteEventsUrl() {
    return '$fullBaseUrl$userFavoriteEvents';
  }

  static String getAddEventToUserFavoritesUrl() {
    return '$fullBaseUrl$addEventToUserFavorites';
  }

  static String getRemoveEventFromUserFavoritesUrl(String eventId) {
    return '$fullBaseUrl$removeEventFromUserFavorites/$eventId';
  }

  static String getCheckIfUserLikedEventUrl() {
    return '$fullBaseUrl$checkIfUserLikedEvent';
  }

  static String getUserToLikeEventUrl() {
    return '$fullBaseUrl$userToLikeEvent';
  }

  static String getUserToUnlikeEventUrl() {
    return '$fullBaseUrl$userToUnlikeEvent';
  }

  static String getUserToSendEventOpinionUrl() {
    return '$fullBaseUrl$userToSendEventOpinion';
  }

  static String getUserToShareEventUrl() {
    return '$fullBaseUrl$userToShareEvent';
  }

  // User job application URL helpers
  static String getApplyJobUrl() {
    return '$fullBaseUrl$userApplyForJob';
  }

  static String getUserJobRecommendationsUrl(String userId) {
    return '$fullBaseUrl$userJobRecommendations/$userId';
  }

  static String getAddUserFavoriteJobPostUrl() {
    return '$fullBaseUrl$addUserFavoriteJobPost';
  }

  static String getRemoveUserFavoriteJobPostUrl() {
    return '$fullBaseUrl$RemoveUserFavoriteJobPost';
  }

  // Blog URL helpers
  static String getAllBlogsUrl() {
    return '$fullBaseUrl$allBlogs';
  }

  static String getBlogByIdUrl(String blogId) {
    return '$fullBaseUrl$blogById/$blogId';
  }

  // Events URL helpers
  static String getAllEventsUrl() {
    return '$fullBaseUrl$allEvents';
  }

  static String getEventByIdUrl(String eventId) {
    return '$fullBaseUrl$eventById/$eventId';
  }

  // Jobs URL helpers
  static String getAllJobPostsUrl() {
    return '$fullBaseUrl$allJobPosts';
  }

  static String getJobPostByIdUrl(String jobId) {
    return '$fullBaseUrl$jobPostById/$jobId';
  }
}
